import { useState, useEffect } from 'react';

export function useUserManagement() {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const createUser = async (userData: any) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newUser = {
        id: Date.now().toString(),
        ...userData,
        createdAt: new Date().toLocaleDateString('fr-FR'),
      };
      
      setUsers(prev => [newUser, ...prev]);
    } catch (error) {
      throw new Error('Erreur lors de la création');
    } finally {
      setLoading(false);
    }
  };

  const updateUser = async (userId: string, userData: any) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, ...userData } : user
      ));
    } catch (error) {
      throw new Error('Erreur lors de la modification');
    } finally {
      setLoading(false);
    }
  };

  const deleteUser = async (userId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setUsers(prev => prev.filter(user => user.id !== userId));
    } catch (error) {
      throw new Error('Erreur lors de la suppression');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Mock data
    setUsers([
      {
        id: '1',
        firstName: 'Jean',
        lastName: 'Dupont',
        email: '<EMAIL>',
        department: 'Informatique',
        role: 'employee',
      },
      {
        id: '2',
        firstName: 'Marie',
        lastName: 'Martin',
        email: '<EMAIL>',
        department: 'RH',
        role: 'manager',
      },
      {
        id: '3',
        firstName: 'Pierre',
        lastName: 'Durand',
        email: '<EMAIL>',
        department: 'Marketing',
        role: 'employee',
      },
    ]);
  }, []);

  return {
    users,
    createUser,
    updateUser,
    deleteUser,
    loading,
  };
}