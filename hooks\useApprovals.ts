import { useState, useEffect } from 'react';

export function useApprovals() {
  const [pendingRequests, setPendingRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const approveRequest = async (requestId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPendingRequests(prev => prev.filter(req => req.id !== requestId));
    } catch (error) {
      throw new Error('Erreur lors de l\'approbation');
    } finally {
      setLoading(false);
    }
  };

  const rejectRequest = async (requestId: string, reason: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPendingRequests(prev => prev.filter(req => req.id !== requestId));
    } catch (error) {
      throw new Error('Erreur lors du refus');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Mock data
    setPendingRequests([
      {
        id: '1',
        employee: { 
          firstName: 'Marie', 
          lastName: '<PERSON>',
          department: 'Informatique'
        },
        type: 'Congé Payé',
        startDate: '20/03/2024',
        endDate: '25/03/2024',
        days: 4,
        comment: 'Vacances de printemps avec la famille',
      },
      {
        id: '2',
        employee: { 
          firstName: 'Pierre', 
          lastName: 'Durand',
          department: 'Marketing'
        },
        type: 'RTT',
        startDate: '18/03/2024',
        endDate: '18/03/2024',
        days: 1,
        comment: 'Rendez-vous médical important',
      },
    ]);
  }, []);

  return {
    pendingRequests,
    approveRequest,
    rejectRequest,
    loading,
  };
}