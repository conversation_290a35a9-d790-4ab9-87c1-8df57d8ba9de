import { View, Text, StyleSheet, FlatList } from 'react-native';
import { useState } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { useAttendanceHistory } from '@/hooks/useAttendanceHistory';
import { Calendar, Clock, MapPin } from 'lucide-react-native';

export default function HistoryScreen() {
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const { history, loading } = useAttendanceHistory(selectedMonth);

  const renderHistoryItem = ({ item }: { item: any }) => (
    <Card style={styles.historyCard}>
      <View style={styles.historyHeader}>
        <View style={styles.dateContainer}>
          <Calendar size={16} color="#6b7280" />
          <Text style={styles.dateText}>{item.date}</Text>
        </View>
        <Badge 
          variant={item.status === 'present' ? 'success' : 'danger'} 
          text={item.status === 'present' ? 'Présent' : 'Absent'} 
        />
      </View>

      {item.status === 'present' && (
        <View style={styles.timeContainer}>
          <View style={styles.timeRow}>
            <Clock size={16} color="#10B981" />
            <Text style={styles.timeText}>Arrivée: {item.clockIn}</Text>
          </View>
          {item.clockOut && (
            <View style={styles.timeRow}>
              <Clock size={16} color="#EF4444" />
              <Text style={styles.timeText}>Départ: {item.clockOut}</Text>
            </View>
          )}
          <View style={styles.timeRow}>
            <MapPin size={16} color="#6b7280" />
            <Text style={styles.timeText}>
              Temps total: {item.totalTime || 'En cours'}
            </Text>
          </View>
        </View>
      )}

      {item.notes && (
        <View style={styles.notesContainer}>
          <Text style={styles.notesText}>{item.notes}</Text>
        </View>
      )}
    </Card>
  );

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Historique</Text>
          <Text style={styles.subtitle}>
            Vos pointages du mois
          </Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>22</Text>
            <Text style={styles.statLabel}>Jours travaillés</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>176h</Text>
            <Text style={styles.statLabel}>Heures totales</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>8.0h</Text>
            <Text style={styles.statLabel}>Moyenne/jour</Text>
          </View>
        </View>

        <FlatList
          data={history}
          renderItem={renderHistoryItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshing={loading}
          onRefresh={() => {}}
        />
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  listContainer: {
    padding: 16,
  },
  historyCard: {
    marginBottom: 12,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  timeContainer: {
    gap: 8,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#374151',
  },
  notesContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  notesText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
  },
});