import { View, Text, StyleSheet } from 'react-native';

interface BadgeProps {
  text: string;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

export function Badge({ text, variant = 'primary' }: BadgeProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'secondary':
        return {
          backgroundColor: '#f3f4f6',
          color: '#374151',
        };
      case 'success':
        return {
          backgroundColor: '#d1fae5',
          color: '#065f46',
        };
      case 'warning':
        return {
          backgroundColor: '#fef3c7',
          color: '#92400e',
        };
      case 'danger':
        return {
          backgroundColor: '#fee2e2',
          color: '#991b1b',
        };
      default:
        return {
          backgroundColor: '#dbeafe',
          color: '#1e40af',
        };
    }
  };

  const variantStyles = getVariantStyles();

  return (
    <View style={[styles.badge, { backgroundColor: variantStyles.backgroundColor }]}>
      <Text style={[styles.text, { color: variantStyles.color }]}>
        {text}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
  },
});