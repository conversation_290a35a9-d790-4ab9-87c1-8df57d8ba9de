import { useState, useEffect } from 'react';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'employee' | 'manager' | 'admin';
  department: string;
  hireDate: string;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>({
    id: '1',
    firstName: '<PERSON>',
    lastName: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'employee',
    department: 'Informatique',
    hireDate: '2023-01-15'
  });

  const [loading, setLoading] = useState(false);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock user data based on email
      const mockUser = {
        id: '1',
        firstName: 'Jean',
        lastName: 'Dupont',
        email,
        role: email.includes('admin') ? 'admin' : email.includes('manager') ? 'manager' : 'employee',
        department: 'Informatique',
        hireDate: '2023-01-15'
      } as User;
      
      setUser(mockUser);
      return mockUser;
    } catch (error) {
      throw new Error('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
  };

  return {
    user,
    login,
    logout,
    loading,
    isAuthenticated: !!user,
  };
}