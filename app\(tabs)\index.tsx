import { View, Text, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useState, useEffect } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Timer } from '@/components/ui/Timer';
import { useAppSelector } from '@/store/store';
import { selectCurrentUser } from '@/store/slices/authSlice';
import {
  useGetAttendanceStatusQuery,
  useClockInMutation,
  useClockOutMutation,
} from '@/store/api/apiSlice';
import { ClockActionPayload } from '@/types/attendance';
import {
  Clock,
  Calendar,
  CircleCheck as CheckCircle,
  LogIn,
  LogOut,
  Briefcase,
  Sun,
} from 'lucide-react-native';
// --- FIX #2: Define props type for the InfoRow component ---
type InfoRowProps = {
  icon: React.ReactNode; // React.ReactNode allows any renderable element (like JSX icons)
  label: string;
  value: string;
  color?: string; // Optional color prop
};

// Apply the props type using React.FC (Function Component)
const InfoRow: React.FC<InfoRowProps> = ({ icon, label, value, color = '#374151' }) => (
  <View style={styles.infoRow}>
    {icon}
    <Text style={styles.infoLabel}>{label}</Text>
    <Text style={[styles.infoValue, { color }]}>{value}</Text>
  </View>
);

// Helper function to format seconds into HH:MM:SS
const formatDuration = (totalSeconds: number): string => {
  if (totalSeconds < 0) totalSeconds = 0;
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  return `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};


export default function HomeScreen() {
  const user = useAppSelector(selectCurrentUser);
  const [currentTime, setCurrentTime] = useState(new Date());
const [currentDate, setCurrentDate] = useState(new Date());
  const [elapsedTime, setElapsedTime] = useState(0);

  // RTK Query hooks
  const {
    data: todayRecord,
    isLoading: isLoadingStatus,
    error: statusError,
    refetch,
  } = useGetAttendanceStatusQuery();

  const [clockIn, { isLoading: isClockingIn }] = useClockInMutation();
  const [clockOut, { isLoading: isClockingOut }] = useClockOutMutation();

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    // Update the date/time string once every second
    const interval = setInterval(() => setCurrentDate(new Date()), 1000);
    return () => clearInterval(interval);
  }, []);
  const getCurrentStatus = () => {
    if (!todayRecord) return 'clocked_out';
    if (todayRecord.clockIn && !todayRecord.clockOut) return 'clocked_in';
    if (todayRecord.clockIn && todayRecord.clockOut) return 'completed';
    return 'clocked_out';
  };
  const getStatusDetails = () => {
    switch (currentStatus) {
      case 'clocked_in':
        return {
          text: 'Vous êtes pointé',
          color: '#22c55e',
          icon: <LogIn size={22} color="#22c55e" />,
        };
      case 'completed':
        return {
          text: 'Journée terminée',
          color: '#3b82f6',
          icon: <CheckCircle size={22} color="#3b82f6" />,
        };
      default:
        return {
          text: 'Prêt à commencer',
          color: '#6b7280',
          icon: <Sun size={22} color="#6b7280" />,
        };
    }
  };
  const currentStatus = getCurrentStatus();

  const handleClockAction = async () => {
    try {
      const payload: ClockActionPayload = {
        action: currentStatus === 'clocked_out' ? 'clock_in' : 'clock_out',
        timestamp: new Date().toISOString(),
        // You can add location data here if needed
      };

      if (currentStatus === 'clocked_out') {
        await clockIn(payload).unwrap();
        Alert.alert('Succès', "Pointage d'entrée enregistré");
      } else {
        await clockOut(payload).unwrap();
        Alert.alert('Succès', 'Pointage de sortie enregistré');
      }
    } catch (error: any) {
      console.error('Clock action error:', error);
      Alert.alert(
        'Erreur',
        error?.data?.message || "Impossible d'enregistrer le pointage"
      );
    }
  };

  const getButtonProps = () => {
    const isLoading = isClockingIn || isClockingOut;

    switch (currentStatus) {
      case 'clocked_out':
        return {
          title: "Pointer l'Entrée",
          variant: 'primary' as const,
          disabled: isLoading,
          loading: isClockingIn,
        };
      case 'clocked_in':
        return {
          title: 'Pointer la Sortie',
          variant: 'danger' as const,
          disabled: isLoading,
          loading: isClockingOut,
        };
      default:
        return {
          title: 'Journée terminée',
          variant: 'secondary' as const,
          disabled: true,
          loading: false,
        };
    }
  };

  const buttonProps = getButtonProps();

  // Function to handle saving timer data to history
  const handleSaveToHistory = (totalTime: number) => {
    const hours = Math.floor(totalTime / 3600);
    const minutes = Math.floor((totalTime % 3600) / 60);
    const timeString = `${hours}h ${minutes}min`;

    Alert.alert(
      'Temps sauvegardé',
      `Temps de travail enregistré: ${timeString}`,
      [{ text: 'OK' }]
    );

    // Here you would typically save to your backend/database
    console.log('Saving to history:', { totalTime, timeString });
  };
  const statusDetails = getStatusDetails();

  // Show loading spinner while fetching attendance status
  if (isLoadingStatus) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <Card style={styles.headerCard}>
          <View>
            <Text style={styles.greeting}>Bonjour, {user?.firstName}!</Text>
            <Text style={styles.dateText}>
              {currentDate.toLocaleDateString('fr-FR', {
                weekday: 'long',
                day: 'numeric',
                month: 'long',
              })}
            </Text>
            <Text style={styles.time}>
              {currentTime.toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </Text>
          </View>
          <View style={styles.statusBadge}>
            {statusDetails.icon}
            <Text style={[styles.statusText, { color: statusDetails.color }]}>
              {statusDetails.text}
            </Text>
          </View>
        </Card>

        {/* Timer Component */}
        <Timer onSaveToHistory={handleSaveToHistory} />

        <View style={styles.clockButton}>
          <Button
            title={buttonProps.title}
            variant={buttonProps.variant}
            size="large"
            disabled={buttonProps.disabled}
            loading={buttonProps.loading}
            onPress={handleClockAction}
          />
        </View>

        <Card style={styles.summaryCard}>
          <Text style={styles.cardTitle}>Résumé du jour</Text>
          <InfoRow
            icon={<LogIn size={20} color="#22c55e" />}
            label="Heure d'arrivée"
            value={
              todayRecord?.clockIn
                ? new Date(todayRecord.clockIn).toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })
                : 'N/A'
            }
            color="#22c55e"
          />
          <InfoRow
            icon={<LogOut size={20} color="#ef4444" />}
            label="Heure de départ"
            value={
              todayRecord?.clockOut
                ? new Date(todayRecord.clockOut).toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })
                : 'N/A'
            }
            color="#ef4444"
          />
          <InfoRow
            icon={<Briefcase size={20} color="#3b82f6" />}
            label="Durée totale"
            value={formatDuration(elapsedTime)}
            color="#3b82f6"
          />
        </Card>

        <Card style={styles.balanceCard}>
          <View style={styles.balanceRow}>
            <Calendar size={20} color="#10B981" />
            <Text style={styles.balanceText}>
              Solde de congés restants : 15 jours
            </Text>
          </View>
        </Card>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    gap: 16,
  },
  // --- Cards ---
  headerCard: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  timerCard: {
    padding: 24,
    alignItems: 'center',
    gap: 20,
    backgroundColor: '#ffffff',
  },
  summaryCard: {
    padding: 20,
    gap: 16,
    backgroundColor: '#ffffff',
  },
  balanceCard: {
    padding: 20,
    gap: 12,
    backgroundColor: '#ffffff',
  },
  // --- Typography & Text ---
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  dateText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  timerLabel: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
  timerText: {
    fontSize: 52,
    fontWeight: 'bold',
    color: '#111827',
    fontFamily: 'monospace', // Gives a digital clock feel
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  // --- Layout & Components ---
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 999,
    backgroundColor: '#f3f4f6',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoLabel: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#4b5563',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  // --- Loading State ---
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  time: {
    fontSize: 18,
    color: '#6b7280',
    fontWeight: '500',
  },
  clockButton: {
    marginBottom: 24,
  },
  recordCard: {
    marginBottom: 16,
  },
  recordRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#374151',
  },
  balanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#374151',
    fontWeight: '600',
  },
  activityCard: {
    marginBottom: 16,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6b7280',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
});
