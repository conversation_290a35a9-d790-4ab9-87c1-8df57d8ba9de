import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from './Button';
import { Card } from './Card';
import { useAppDispatch, useAppSelector } from '@/store/store';
import {
  startTimer,
  pauseTimer,
  selectIsTimerRunning,
  selectTimerState,
} from '@/store/slices/timerSlice';
import { Play, Pause } from 'lucide-react-native';

interface TimerProps {
  onSaveToHistory?: (totalTime: number) => void;
}

export const Timer: React.FC<TimerProps> = ({ onSaveToHistory }) => {
  const dispatch = useAppDispatch();
  const isRunning = useAppSelector(selectIsTimerRunning);
  const timerState = useAppSelector(selectTimerState);

  // Local state to hold the current display time
  const [displayTime, setDisplayTime] = useState(0);

  // Debug logs
  console.log(
    '[Timer] Render - isRunning:',
    isRunning,
    'timerState:',
    timerState,
    'displayTime:',
    displayTime
  );

  // Update display time based on timer state
  useEffect(() => {
    const updateDisplayTime = () => {
      if (timerState.isRunning && timerState.startTime !== null) {
        const currentTime = Date.now();
        const sessionDuration = Math.floor(
          (currentTime - timerState.startTime) / 1000
        );
        const totalTime = timerState.elapsedTime + sessionDuration;
        setDisplayTime(totalTime);
        console.log(
          '[Timer] Updated display time:',
          totalTime,
          'sessionDuration:',
          sessionDuration
        );
      } else {
        setDisplayTime(timerState.elapsedTime);
        console.log(
          '[Timer] Timer paused, display time:',
          timerState.elapsedTime
        );
      }
    };

    // Update immediately
    updateDisplayTime();

    // Set up interval if timer is running
    let intervalId: number | null = null;
    if (timerState.isRunning) {
      intervalId = setInterval(updateDisplayTime, 1000);
      console.log('[Timer] Started interval for real-time updates');
    }

    // Cleanup function
    return () => {
      if (intervalId !== null) {
        clearInterval(intervalId);
        console.log('[Timer] Cleared interval');
      }
    };
  }, [timerState.isRunning, timerState.startTime, timerState.elapsedTime]);

  // Format time from seconds to HH:MM:SS
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleToggle = () => {
    console.log('[Timer] Toggle clicked - current isRunning:', isRunning);
    if (isRunning) {
      console.log('[Timer] Dispatching pauseTimer');
      dispatch(pauseTimer());
    } else {
      console.log('[Timer] Dispatching startTimer');
      dispatch(startTimer());
    }
  };

  return (
    <Card style={styles.container}>
      <View style={styles.timerDisplay}>
        <Text style={styles.timerText}>{formatTime(displayTime)}</Text>
        <Text style={styles.timerLabel}>Temps de travail</Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={isRunning ? 'Arrêter' : 'Démarrer'}
          variant={isRunning ? 'danger' : 'primary'}
          size="large"
          onPress={handleToggle}
          style={styles.toggleButton}
          icon={
            isRunning ? (
              <Pause size={20} color="#ffffff" />
            ) : (
              <Play size={20} color="#ffffff" />
            )
          }
        />
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  timerDisplay: {
    alignItems: 'center',
    marginBottom: 32,
  },
  timerText: {
    fontSize: 56,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'monospace',
    letterSpacing: 2,
  },
  timerLabel: {
    fontSize: 18,
    color: '#6b7280',
    marginTop: 12,
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  toggleButton: {
    minWidth: 200,
    paddingHorizontal: 32,
  },
});
