import { useState, useEffect } from 'react';

type AttendanceStatus = 'clocked_out' | 'clocked_in' | 'completed';

interface TodayRecord {
  clockIn: string | null;
  clockOut: string | null;
}

export function useAttendance() {
  const [currentStatus, setCurrentStatus] =
    useState<AttendanceStatus>('clocked_out');
  const [todayRecord, setTodayRecord] = useState<TodayRecord | null>(null);
  const [leaveBalance, setLeaveBalance] = useState(15);

  const clockIn = async () => {
    try {
      const now = new Date();
      const time = now.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
      });

      setCurrentStatus('clocked_in');
      setTodayRecord({ clockIn: time, clockOut: null });

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      throw new Error('Erreur lors du pointage');
    }
  };

  const clockOut = async () => {
    try {
      const now = new Date();
      const time = now.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
      });

      setCurrentStatus('completed');
      setTodayRecord((prev) =>
        prev ? { ...prev, clockOut: time } : { clockIn: null, clockOut: time }
      );

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      throw new Error('Erreur lors du pointage');
    }
  };

  useEffect(() => {
    // Initialize with mock data
    setTodayRecord({ clockIn: '09:01', clockOut: null });
  }, []);

  return {
    currentStatus,
    clockIn,
    clockOut,
    todayRecord,
    leaveBalance,
  };
}
