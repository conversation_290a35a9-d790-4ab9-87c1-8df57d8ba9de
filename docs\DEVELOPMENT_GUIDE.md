# **Guide de Développement Stratégique - AttendanceApp Mobile (Édition Redux)**

## **Introduction & Philosophie du Projet**

Bienvenue dans le guide de développement de **AttendanceApp**. Ce document est votre plan stratégique pour construire une application de qualité professionnelle en utilisant une architecture robuste et standard de l'industrie.

Notre philosophie repose sur trois piliers :

1.  **Architecture Solide et Prévisible :** Nous utilisons **Redux Toolkit** pour une gestion de l'état centralisée, traçable et évolutive. Le code est plus facile à déboguer et à maintenir.
2.  **Expérience Utilisateur (UX) au Cœur :** L'application doit être rapide, intuitive et fiable. **RTK Query** nous aidera à atteindre cet objectif en optimisant la gestion des données serveur.
3.  **Qualité et Tests Intégrés :** Les tests sont intégrés dans le flux de travail pour garantir la stabilité et éviter les régressions.

> **Pourquoi Redux Toolkit ?**
> Pour un projet avec plusieurs rôles, des données partagées (congés, pointages) et de nombreuses interactions avec une API, Redux Toolkit offre :
>
> - **Un Store Unique :** Une seule source de vérité, facile à déboguer avec les Redux DevTools.
> - **RTK Query :** Une solution puissante pour gérer le cycle de vie des données API (fetching, caching, revalidation), ce qui élimine une énorme quantité de code répétitif.
> - **Prévisibilité :** Des règles claires sur la manière de modifier l'état, ce qui est crucial pour les équipes et la maintenance à long terme.

---

## 1. Structure et Configuration Initiale

### 1.1 Installation de l'Environnement

#### Création du Projet et Dépendances

```bash
# Créer le projet Expo avec le template de base
npx create-expo-app AttendanceApp --template

# Naviguer dans le dossier
cd AttendanceApp

# Installer les dépendances pour Redux
npm install @reduxjs/toolkit react-redux

# Installer les dépendances de navigation (Expo Router)
npm install expo-router react-native-safe-area-context react-native-screens expo-linking expo-constants expo-status-bar
npx expo install react-native-gesture-handler react-native-reanimated

# Installer les autres dépendances essentielles
npm install lucide-react-native react-native-svg
npm install axios # Utile pour des cas spécifiques non couverts par RTK Query
npm install @react-native-async-storage/async-storage
npx expo install expo-local-authentication expo-secure-store
```

### 1.2 Architecture des Dossiers (Adaptée pour Redux)

```
AttendanceApp/
├── app/                          # ✅ Cœur de la navigation (Expo Router)
├── components/                   # Composants réutilisables
├── services/                     # Logique externe (principalement le stockage sécurisé)
│   └── storage.ts
├── store/                        # 🧠 LE CERVEAU REDUX
│   ├── slices/                   # Les "tranches" de notre état global
│   │   ├── authSlice.ts          # Gère l'état de l'authentification (user, token)
│   │   └── uiSlice.ts            # (Optionnel) Gère l'état de l'UI (thème, modales globales)
│   ├── api/                      # Définitions de RTK Query
│   │   └── apiSlice.ts           # Le point d'entrée unique pour toutes les requêtes API
│   └── store.ts                  # Configuration du store Redux
├── types/                        # Définitions TypeScript
└── utils/                        # Fonctions utilitaires
```

---

## 2. Phase 1 - Fondations : Mettre en Place Redux

_C'est l'étape la plus importante. Une bonne configuration Redux rendra le reste du développement beaucoup plus simple._

### 2.1 Création du Slice d'Authentification

Le `authSlice` gérera l'état de l'utilisateur connecté.

```typescript
// store/slices/authSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from '@/types/auth';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
}

const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Action appelée après une connexion réussie
    setCredentials(
      state,
      action: PayloadAction<{ user: User; token: string }>
    ) {
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.isAuthenticated = true;
    },
    // Action appelée lors de la déconnexion
    logOut(state) {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
    },
  },
});

export const { setCredentials, logOut } = authSlice.actions;
export default authSlice.reducer;
```

### 2.2 Création de l'API Slice avec RTK Query

C'est la partie la plus puissante. Ce fichier unique définira **toutes** nos interactions avec l'API.

```typescript
// store/api/apiSlice.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { RootState } from '../store'; // On importera RootState plus tard

// Définir le point d'entrée de notre API
export const apiSlice = createApi({
  reducerPath: 'api', // Nom du reducer dans le store
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.EXPO_PUBLIC_API_URL,
    // Ajouter le token d'authentification à chaque requête
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['User', 'Leave', 'Attendance'], // Tags pour invalider le cache
  endpoints: (builder) => ({
    // Endpoint pour la connexion
    login: builder.mutation<{ token: string; user: User }, LoginCredentials>({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
    }),
    // Endpoint pour récupérer le statut de pointage du jour
    getAttendanceStatus: builder.query<AttendanceRecord, void>({
      query: () => '/attendance/status',
      providesTags: ['Attendance'], // Ce query est tagué 'Attendance'
    }),
    // Endpoint pour pointer
    clockIn: builder.mutation<AttendanceRecord, ClockActionPayload>({
      query: (payload) => ({
        url: '/attendance/clock-in',
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['Attendance'], // Pointer invalide le cache 'Attendance'
    }),
    // ... AJOUTER TOUS LES AUTRES ENDPOINTS ICI (getLeaves, approveLeave, etc.)
  }),
});

// Exporter les hooks générés automatiquement
export const {
  useLoginMutation,
  useGetAttendanceStatusQuery,
  useClockInMutation,
  // ... exporter les autres hooks
} = apiSlice;
```

### 2.3 Configuration du Store

Assemblons les pièces.

```typescript
// store/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { apiSlice } from './api/apiSlice';
import authReducer from './slices/authSlice';

export const store = configureStore({
  reducer: {
    // Ajouter le reducer de l'API Slice
    [apiSlice.reducerPath]: apiSlice.reducer,
    // Ajouter le reducer de l'auth slice
    auth: authReducer,
  },
  // Le middleware est nécessaire pour RTK Query (caching, invalidation...)
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(apiSlice.middleware),
});

// Inférence des types pour une utilisation avec TypeScript
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### 2.4 Connexion du Store à l'Application

Il faut "envelopper" notre application avec le `Provider` de Redux.

```typescript
// app/_layout.tsx (Layout Racine)
import { Provider } from 'react-redux';
import { store } from '@/store/store';
import { Slot } from 'expo-router';
import { AuthNavigator } from '@/navigation/AuthNavigator'; // Un composant qui gère la redirection

function RootLayoutNav() {
  return (
    // Ce composant va maintenant gérer la redirection
    <AuthNavigator />
  );
}

export default function RootLayout() {
  return (
    <Provider store={store}>
      <RootLayoutNav />
    </Provider>
  );
}

// navigation/AuthNavigator.tsx (Anciennement la logique du RootLayout)
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useRouter, useSegments, Slot } from 'expo-router';
import { RootState } from '@/store/store';

export function AuthNavigator() {
  const isAuthenticated = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );
  // ... (même logique de redirection que dans la version précédente, mais avec useSelector)
  return <Slot />;
}
```

#### ✅ **Point de Contrôle 1 : Définition de "Terminé"**

- [ ] Le store Redux est configuré.
- [ ] Le `authSlice` est créé.
- [ ] Le `apiSlice` est créé avec au moins l'endpoint de `login`.
- [ ] L'application est enveloppée dans le `Provider` Redux.

---

## 3. Phase 2 - Fonctionnalités Employé (avec Redux)

### 3.1 Système d'Authentification (Refactorisé)

L'écran de connexion va maintenant utiliser le hook `useLoginMutation`.

```typescript
// app/(auth)/login.tsx
import { useDispatch } from 'react-redux';
import { useLoginMutation } from '@/store/api/apiSlice';
import { setCredentials } from '@/store/slices/authSlice';
import { Alert } from 'react-native';

export default function LoginScreen() {
  const dispatch = useDispatch();
  // Le hook de mutation renvoie la fonction de déclenchement et l'état de la requête
  const [login, { isLoading }] = useLoginMutation();

  const handleLogin = async (credentials: LoginCredentials) => {
    try {
      // `unwrap()` rejette une promesse en cas d'erreur, ce qui permet un try/catch propre
      const { user, token } = await login(credentials).unwrap();

      // En cas de succès, on dispatch l'action pour mettre à jour le store
      dispatch(setCredentials({ user, token }));

      // Le AuthNavigator s'occupera de la redirection
    } catch (err) {
      Alert.alert(
        'Erreur',
        err.data?.message || 'Email ou mot de passe incorrect'
      );
    }
  };

  // ... Le reste du JSX utilise `isLoading` et appelle `handleLogin`
}
```

### 3.2 Interface de Pointage (Refactorisée)

L'écran d'accueil utilise maintenant les hooks de RTK Query. Fini la gestion manuelle du `loading` !

```typescript
// app/(tabs)/index.tsx
import {
  useGetAttendanceStatusQuery,
  useClockInMutation,
} from '@/store/api/apiSlice';

export default function HomeScreen() {
  // Le hook de query gère automatiquement le fetching, le cache et les états
  const {
    data: todayRecord,
    isLoading,
    isError,
    refetch,
  } = useGetAttendanceStatusQuery();

  const [clockIn, { isLoading: isClockingIn }] = useClockInMutation();

  const handleClockAction = async () => {
    try {
      await clockIn({
        /* payload */
      }).unwrap();
      // RTK Query va automatiquement refetcher les données taguées 'Attendance' grâce à `invalidatesTags`
      Alert.alert('Succès', 'Pointage enregistré !');
    } catch (err) {
      Alert.alert('Erreur', 'Le pointage a échoué.');
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // ... Le reste du JSX utilise `todayRecord`, `isClockingIn`, etc.
}
```

> **La Magie de RTK Query :** Remarquez comme le code est devenu plus simple. Plus de `useState` pour `loading`, `error`, `data`. Plus d'appels manuels pour rafraîchir les données. RTK Query gère tout cela pour nous.

---

## 4. Phase 3 & 4 - Fonctionnalités Manager & Admin

La stratégie reste la même :

1.  **Définir les Endpoints :** Ajoutez tous les endpoints nécessaires dans `store/api/apiSlice.ts` (ex: `getPendingLeaves`, `approveLeave`, `getAllUsers`, etc.). N'oubliez pas d'utiliser `providesTags` et `invalidatesTags` pour une gestion de cache intelligente.
2.  **Utiliser les Hooks :** Dans vos écrans (`approvals.tsx`, `users.tsx`), utilisez les hooks générés par RTK Query (`useGetPendingLeavesQuery`, `useApproveLeaveMutation`, etc.).
3.  **Contrôle d'Accès :** La navigation conditionnelle basée sur le rôle de l'utilisateur (lu depuis le store Redux avec `useSelector`) reste identique et cruciale.

---

## 6. Phase 5 - Optimisations et Finalisation

### 6.2 Support Hors-Ligne (Approche avec Redux)

Redux peut aider à gérer le mode hors-ligne de manière plus structurée.

**Stratégie :**

1.  **Middleware Personnalisé :** Créez un middleware Redux qui intercepte les actions de mutation (comme `clockIn.initiate`).
2.  **Détection de Connexion :** Si l'appareil est hors ligne, le middleware annule l'action API et la stocke à la place dans `AsyncStorage` avec un statut "en attente".
3.  **Synchronisation :** Lorsque l'application revient en ligne, un service de synchronisation lit les actions en attente et les "re-dispatche" une par une.
4.  **Optimistic Updates :** Pour une meilleure UX, le middleware peut aussi mettre à jour l'état de l'UI de manière optimiste (comme si l'action avait réussi), puis annuler le changement si la synchronisation échoue.

Cette approche est plus complexe mais extrêmement robuste.

---

## Conclusion (Édition Redux)

En adoptant Redux Toolkit, et plus particulièrement RTK Query, vous ne faites pas que gérer l'état de votre application ; vous adoptez une architecture de données complète, éprouvée et extrêmement efficace. Le coût initial de configuration est largement compensé par la simplification du code des fonctionnalités, la réduction des bugs et une expérience de débogage supérieure.
