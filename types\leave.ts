export interface LeaveRequest {
  id: string;
  userId: string;
  type: 'vacation' | 'sick' | 'personal' | 'maternity' | 'paternity' | 'emergency';
  startDate: string;
  endDate: string;
  totalDays: number;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  reason: string;
  comment?: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectedReason?: string;
  createdAt: string;
  updatedAt: string;
  documents?: string[];
}

export interface LeaveBalance {
  userId: string;
  vacation: {
    total: number;
    used: number;
    remaining: number;
  };
  sick: {
    total: number;
    used: number;
    remaining: number;
  };
  personal: {
    total: number;
    used: number;
    remaining: number;
  };
  year: number;
}

export interface CreateLeaveRequest {
  type: LeaveRequest['type'];
  startDate: string;
  endDate: string;
  reason: string;
  comment?: string;
  documents?: string[];
}

export interface ApprovalAction {
  requestId: string;
  action: 'approve' | 'reject';
  comment?: string;
}
