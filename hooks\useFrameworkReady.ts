// In file: hooks/useFrameworkReady.ts

import { useState, useEffect } from 'react';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';

// This is a critical best practice: prevent the splash screen from auto-hiding
// before we are ready to do it ourselves.
SplashScreen.preventAutoHideAsync();

export function useFrameworkReady(): boolean {
  const [isAppReady, setIsAppReady] = useState(false);

  // 1. Load any custom fonts you need.
  // Replace with your actual font files. If you don't have any, you can
  // leave the object empty: useFonts({})
  const [fontsLoaded, fontError] = useFonts({
    // Add other fonts here
  });

  useEffect(() => {
    // The app is "ready" when the fonts have loaded (or an error occurred).
    if (fontsLoaded || fontError) {
      // Hide the splash screen now that we are ready to render the first screen.
      SplashScreen.hideAsync();

      // Tell our component that we are ready.
      setIsAppReady(true);
    }
  }, [fontsLoaded, fontError]);

  // 2. Return the boolean state.
  // It will be 'false' initially, and 'true' once the useEffect runs.
  return isAppReady;
}
