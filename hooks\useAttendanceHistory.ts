import { useState, useEffect } from 'react';

export function useAttendanceHistory(month: number) {
  const [history, setHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Mock data
    const mockHistory = [
      {
        id: '1',
        date: 'Lundi 18 Mars',
        status: 'present',
        clockIn: '09:01',
        clockOut: '18:15',
        totalTime: '8h 14min',
      },
      {
        id: '2',
        date: 'Mardi 19 Mars',
        status: 'present',
        clockIn: '08:58',
        clockOut: '17:45',
        totalTime: '7h 47min',
      },
      {
        id: '3',
        date: 'Mercredi 20 Mars',
        status: 'absent',
        notes: 'Congé maladie',
      },
      {
        id: '4',
        date: 'Jeudi 21 Mars',
        status: 'present',
        clockIn: '09:05',
        clockOut: '18:30',
        totalTime: '8h 25min',
      },
      {
        id: '5',
        date: 'Vendredi 22 Mars',
        status: 'present',
        clockIn: '09:00',
        clockOut: null,
        totalTime: null,
      },
    ];

    setHistory(mockHistory);
  }, [month]);

  return {
    history,
    loading,
  };
}