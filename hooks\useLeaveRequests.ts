import { useState, useEffect } from 'react';

export function useLeaveRequests() {
  const [requests, setRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const createRequest = async (formData: any) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newRequest = {
        id: Date.now().toString(),
        ...formData,
        status: 'pending',
        createdAt: new Date().toLocaleDateString('fr-FR'),
      };
      
      setRequests(prev => [newRequest, ...prev]);
    } catch (error) {
      throw new Error('Erreur lors de la création');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Mock data
    setRequests([
      {
        id: '1',
        type: 'Congé Payé',
        startDate: '15/03/2024',
        endDate: '19/03/2024',
        status: 'pending',
        comment: 'Vacances familiales',
      },
      {
        id: '2',
        type: 'Congé Maladie',
        startDate: '01/03/2024',
        endDate: '02/03/2024',
        status: 'approved',
        comment: 'Grippe',
      },
    ]);
  }, []);

  return {
    requests,
    createRequest,
    loading,
  };
}