import { View, Text, StyleSheet } from 'react-native';
import { useAppSelector } from '@/store/store';
import { selectCurrentUser, selectIsAuthenticated, selectAuthLoading } from '@/store/slices/authSlice';

export function AuthDebug() {
  const user = useAppSelector(selectCurrentUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector(selectAuthLoading);

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🐛 Auth Debug</Text>
      <Text style={styles.text}>Loading: {isLoading ? 'Yes' : 'No'}</Text>
      <Text style={styles.text}>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</Text>
      <Text style={styles.text}>User: {user ? `${user.firstName} ${user.lastName} (${user.role})` : 'None'}</Text>
      <Text style={styles.text}>Email: {user?.email || 'None'}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 10,
    borderRadius: 8,
    zIndex: 1000,
  },
  title: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  text: {
    color: '#ffffff',
    fontSize: 10,
    marginBottom: 2,
  },
});
