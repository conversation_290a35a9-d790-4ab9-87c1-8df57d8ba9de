import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { useState } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { InputField } from '@/components/ui/InputField';
import { useAppSelector } from '@/store/store';
import { selectCurrentUser } from '@/store/slices/authSlice';
import {
  Settings as SettingsIcon,
  Bell,
  Shield,
  Clock,
  Mail,
  Database,
  Download,
  Upload,
  Trash2,
  ChevronRight,
} from 'lucide-react-native';

export default function SettingsScreen() {
  const user = useAppSelector(selectCurrentUser);

  // Route guard: Only admins can access this screen
  if (!user || user.role !== 'admin') {
    return (
      <ScreenWrapper>
        <View style={styles.unauthorizedContainer}>
          <Text style={styles.unauthorizedText}>Accès non autorisé</Text>
          <Text style={styles.unauthorizedSubtext}>
            Cette page est réservée aux administrateurs.
          </Text>
        </View>
      </ScreenWrapper>
    );
  }
  const [notifications, setNotifications] = useState(true);
  const [autoApproval, setAutoApproval] = useState(false);
  const [workingHours, setWorkingHours] = useState({
    start: '09:00',
    end: '17:00',
  });
  const [emailSettings, setEmailSettings] = useState({
    server: 'smtp.company.com',
    port: '587',
    username: '<EMAIL>',
  });

  const handleExportData = () => {
    Alert.alert(
      'Exporter les données',
      "Voulez-vous exporter toutes les données d'attendance ?",
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Exporter',
          onPress: () => {
            Alert.alert(
              'Succès',
              'Export en cours... Vous recevrez un email avec le fichier.'
            );
          },
        },
      ]
    );
  };

  const handleImportData = () => {
    Alert.alert(
      'Importer les données',
      'Cette action remplacera les données existantes. Êtes-vous sûr ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Importer',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Succès', 'Import terminé avec succès.');
          },
        },
      ]
    );
  };

  const handleClearData = () => {
    Alert.alert(
      'Effacer les données',
      'ATTENTION: Cette action supprimera définitivement toutes les données. Cette action est irréversible.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Données supprimées',
              'Toutes les données ont été effacées.'
            );
          },
        },
      ]
    );
  };

  const SettingItem = ({
    icon,
    title,
    subtitle,
    onPress,
    rightElement,
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
  }) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingIcon}>{icon}</View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      <View style={styles.settingRight}>
        {rightElement || <ChevronRight size={20} color="#6b7280" />}
      </View>
    </TouchableOpacity>
  );

  return (
    <ScreenWrapper>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Paramètres</Text>
          <Text style={styles.subtitle}>Configuration du système</Text>
        </View>

        {/* General Settings */}
        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Général</Text>

          <SettingItem
            icon={<Bell size={20} color="#3b82f6" />}
            title="Notifications"
            subtitle="Activer les notifications push"
            rightElement={
              <Switch
                value={notifications}
                onValueChange={setNotifications}
                trackColor={{ false: '#f3f4f6', true: '#3b82f6' }}
                thumbColor={notifications ? '#ffffff' : '#ffffff'}
              />
            }
          />

          <SettingItem
            icon={<Shield size={20} color="#10B981" />}
            title="Approbation automatique"
            subtitle="Approuver automatiquement les demandes de congé"
            rightElement={
              <Switch
                value={autoApproval}
                onValueChange={setAutoApproval}
                trackColor={{ false: '#f3f4f6', true: '#10B981' }}
                thumbColor={autoApproval ? '#ffffff' : '#ffffff'}
              />
            }
          />
        </Card>

        {/* Working Hours */}
        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Heures de Travail</Text>

          <View style={styles.timeContainer}>
            <View style={styles.timeField}>
              <Text style={styles.timeLabel}>Début</Text>
              <InputField
                value={workingHours.start}
                onChangeText={(text) =>
                  setWorkingHours({ ...workingHours, start: text })
                }
                placeholder="09:00"
                style={styles.timeInput}
              />
            </View>
            <View style={styles.timeField}>
              <Text style={styles.timeLabel}>Fin</Text>
              <InputField
                value={workingHours.end}
                onChangeText={(text) =>
                  setWorkingHours({ ...workingHours, end: text })
                }
                placeholder="17:00"
                style={styles.timeInput}
              />
            </View>
          </View>
        </Card>

        {/* Email Configuration */}
        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Configuration Email</Text>

          <InputField
            label="Serveur SMTP"
            value={emailSettings.server}
            onChangeText={(text) =>
              setEmailSettings({ ...emailSettings, server: text })
            }
            placeholder="smtp.company.com"
          />
          <InputField
            label="Port"
            value={emailSettings.port}
            onChangeText={(text) =>
              setEmailSettings({ ...emailSettings, port: text })
            }
            placeholder="587"
            keyboardType="numeric"
          />
          <InputField
            label="Nom d'utilisateur"
            value={emailSettings.username}
            onChangeText={(text) =>
              setEmailSettings({ ...emailSettings, username: text })
            }
            placeholder="<EMAIL>"
            keyboardType="email-address"
          />
        </Card>

        {/* Data Management */}
        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Gestion des Données</Text>

          <SettingItem
            icon={<Download size={20} color="#3b82f6" />}
            title="Exporter les données"
            subtitle="Télécharger toutes les données d'attendance"
            onPress={handleExportData}
          />

          <SettingItem
            icon={<Upload size={20} color="#F59E0B" />}
            title="Importer les données"
            subtitle="Charger des données depuis un fichier"
            onPress={handleImportData}
          />

          <SettingItem
            icon={<Trash2 size={20} color="#EF4444" />}
            title="Effacer toutes les données"
            subtitle="Supprimer définitivement toutes les données"
            onPress={handleClearData}
          />
        </Card>

        <View style={styles.footer}>
          <Button
            title="Sauvegarder les paramètres"
            onPress={() =>
              Alert.alert('Succès', 'Paramètres sauvegardés avec succès')
            }
            style={styles.saveButton}
          />
        </View>
      </ScrollView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  card: {
    margin: 16,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  settingIcon: {
    width: 40,
    alignItems: 'center',
  },
  settingContent: {
    flex: 1,
    marginLeft: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  settingRight: {
    marginLeft: 12,
  },
  timeContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  timeField: {
    flex: 1,
  },
  timeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  timeInput: {
    textAlign: 'center',
  },
  footer: {
    padding: 20,
  },
  saveButton: {
    marginTop: 20,
  },
  unauthorizedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  unauthorizedText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ef4444',
    textAlign: 'center',
  },
  unauthorizedSubtext: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 8,
  },
});
