import { useState, useEffect } from 'react';

export function useManagerData() {
  const [pendingRequests, setPendingRequests] = useState<any[]>([]);
  const [teamAbsences, setTeamAbsences] = useState<any>({
    onLeave: [],
    absent: []
  });
  const [todayStats, setTodayStats] = useState({
    totalEmployees: 15,
    onLeave: 2,
    absent: 1,
  });
  const [weeklyStats, setWeeklyStats] = useState({
    attendanceRate: 92,
  });

  useEffect(() => {
    // Mock data
    setPendingRequests([
      {
        id: '1',
        employee: { firstName: '<PERSON>', lastName: '<PERSON>' },
        type: 'Congé Payé',
        startDate: '20/03/2024',
        endDate: '25/03/2024',
        days: 4,
        comment: 'Vacances de printemps',
      },
      {
        id: '2',
        employee: { firstName: '<PERSON>', lastName: 'Du<PERSON>' },
        type: 'RTT',
        startDate: '18/03/2024',
        endDate: '18/03/2024',
        days: 1,
        comment: 'Rendez-vous médical',
      },
    ]);

    setTeamAbsences({
      onLeave: [
        {
          firstName: 'Sophie',
          lastName: 'Bernard',
          leaveType: 'Congé Payé',
          returnDate: '22/03/2024',
        },
        {
          firstName: 'Thomas',
          lastName: 'Petit',
          leaveType: 'Congé Maladie',
          returnDate: '20/03/2024',
        },
      ],
      absent: [
        {
          firstName: 'Julie',
          lastName: 'Moreau',
        },
      ],
    });
  }, []);

  return {
    pendingRequests,
    teamAbsences,
    todayStats,
    weeklyStats,
  };
}