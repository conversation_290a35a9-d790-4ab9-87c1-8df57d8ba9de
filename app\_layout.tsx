// In file: app/_layout.tsx

import { useEffect } from 'react';
import { Stack, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { store, useAppSelector } from '@/store/store';
import { selectIsAuthenticated } from '@/store/slices/authSlice';
import { AuthService } from '@/services/authService';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';

// This is our main navigation component with debug logs
function RootLayoutNav() {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isReady = useFrameworkReady();

  // DEBUG LOG 1: See what the component thinks the auth state is on EVERY render.
  console.log(
    `[RootLayoutNav] Rendering. isAuthenticated = ${isAuthenticated}, isReady = ${isReady}`
  );

  // Initialize auth state once on app load
  useEffect(() => {
    console.log('[RootLayoutNav] Initializing auth service...');
    AuthService.initializeAuth();
  }, []);

  // This is the CRITICAL effect that handles redirection
  useEffect(() => {
    // DEBUG LOG 2: See if this effect is even running.
    console.log(
      `[RootLayoutEffect] Firing. Dependencies are: isAuthenticated=${isAuthenticated}, isReady=${isReady}`
    );

    if (!isReady) {
      console.log('[RootLayoutEffect] Bailing out: Framework not ready yet.');
      return;
    }

    if (isAuthenticated) {
      // DEBUG LOG 3: See if we are attempting to redirect to tabs.
      console.log(
        '[RootLayoutEffect] User is Authenticated. Redirecting to /tabs...'
      );
      router.replace('/(tabs)');
    } else {
      // DEBUG LOG 4: See if we are attempting to redirect to login.
      console.log(
        '[RootLayoutEffect] User is NOT Authenticated. Redirecting to /login...'
      );
      router.replace('/login');
    }
  }, [isAuthenticated, isReady]); // Re-run this effect when auth status or readiness changes

  return (
    <>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="login" />
        <Stack.Screen name="(tabs)" />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </>
  );
}

// The RootLayout with the Redux Provider
export default function RootLayout() {
  return (
    <Provider store={store}>
      <RootLayoutNav />
    </Provider>
  );
}
