export interface AttendanceRecord {
  id: string;
  userId: string;
  date: string;
  clockIn?: string;
  clockOut?: string;
  totalTime?: string;
  status: 'present' | 'absent' | 'late' | 'half_day';
  notes?: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
}

export interface ClockActionPayload {
  action: 'clock_in' | 'clock_out';
  timestamp: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  notes?: string;
}

export interface AttendanceHistory {
  records: AttendanceRecord[];
  totalDays: number;
  presentDays: number;
  absentDays: number;
  lateDays: number;
  averageHours: string;
}

export interface AttendanceStats {
  today: AttendanceRecord | null;
  thisWeek: {
    totalHours: string;
    daysPresent: number;
    daysAbsent: number;
  };
  thisMonth: {
    totalHours: string;
    daysPresent: number;
    daysAbsent: number;
    attendanceRate: number;
  };
}
